from playwright.sync_api import sync_playwright
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake homepage ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/homepage"
    page.goto(start_url)
    print("Please log in to Snowflake...")
    
    input("Press Enter when you're logged in and can see the homepage...")
    
    # === Step 2: Navigate to Notebooks ===
    print("Navigating to Notebooks...")
    try:
        page.wait_for_timeout(3000)
        notebooks_tab = page.locator('[role="tab"]:has-text("Notebooks")')
        if notebooks_tab.count() > 0:
            notebooks_tab.click()
        else:
            page.locator('text="Notebooks"').first.click()
        page.wait_for_timeout(3000)
        print("✅ Clicked on Notebooks tab")
    except Exception as e:
        print(f"Error clicking Notebooks: {e}")
        input("Please manually click on the 'Notebooks' tab and press Enter...")
    
    # === Step 3: Manual notebook selection ===
    print("\n=== MANUAL NOTEBOOK PROCESSING ===")
    print("I'll help you download each notebook one by one.")
    print("For each notebook:")
    print("1. Click on the notebook name to open it")
    print("2. Wait for it to load completely")
    print("3. I'll try to find and click the download button automatically")
    
    notebook_names = [
        "MPRASANNAPAUL 2025-07-25 16:54:13",
        "Translate multilingual customer reviews",
        "Reference cells and variables in Notebooks",
        "MPRASANNAPAUL 2025-07-25 16:59:15"
    ]
    
    for i, notebook_name in enumerate(notebook_names, 1):
        print(f"\n--- Notebook {i}/4: {notebook_name} ---")
        print("Please click on this notebook in the table to open it")
        input("Press Enter when the notebook is fully loaded...")
        
        # Try to download automatically
        success = download_current_notebook(page, notebook_name, download_dir)
        
        if success:
            print(f"✅ Successfully downloaded: {notebook_name}")
        else:
            print(f"❌ Download failed for: {notebook_name}")
        
        if i < len(notebook_names):
            print("Please navigate back to the Notebooks list")
            input("Press Enter when you're back at the notebooks list...")
    
    print("\n✅ All notebooks processed!")
    
    # List downloaded files
    print(f"\n=== DOWNLOADED FILES ===")
    try:
        files = [f for f in os.listdir(download_dir) if f.endswith('.sql')]
        if files:
            for file in files:
                print(f"✅ {file}")
        else:
            print("No .sql files found")
    except:
        print("Could not list files")
    
    browser.close()

def download_current_notebook(page, notebook_name, download_dir):
    """Try to automatically download the currently open notebook"""
    print("Analyzing the current page...")
    
    # Wait for page to load
    page.wait_for_timeout(5000)
    
    # Take screenshot for debugging
    screenshot_path = os.path.join(download_dir, f"debug_{notebook_name.replace(' ', '_')[:20]}.png")
    page.screenshot(path=screenshot_path)
    print(f"Screenshot saved: {screenshot_path}")
    
    # Debug: Show page title and URL
    try:
        title = page.title()
        url = page.url
        print(f"Page title: {title}")
        print(f"Page URL: {url}")
    except:
        pass
    
    # Look for ALL buttons and analyze them
    print("\n=== ANALYZING ALL BUTTONS ===")
    try:
        all_buttons = page.locator('button:visible')
        button_count = all_buttons.count()
        print(f"Found {button_count} visible buttons")
        
        potential_menu_buttons = []
        
        for i in range(min(20, button_count)):
            try:
                button = all_buttons.nth(i)
                text = button.inner_text().strip()
                aria_label = button.get_attribute('aria-label') or ''
                title = button.get_attribute('title') or ''
                
                print(f"Button {i}: '{text}' | aria: '{aria_label}' | title: '{title}'")
                
                # Check if this could be a menu button
                if ('...' in text or 
                    'more' in aria_label.lower() or 
                    'menu' in aria_label.lower() or
                    'more' in title.lower() or
                    'action' in aria_label.lower()):
                    potential_menu_buttons.append((i, text, aria_label, title))
                    print(f"  *** POTENTIAL MENU BUTTON ***")
                    
            except Exception as btn_error:
                print(f"Button {i}: Error reading - {btn_error}")
        
        print(f"\nFound {len(potential_menu_buttons)} potential menu buttons")
        
        # Try to click potential menu buttons
        for btn_index, text, aria_label, title in potential_menu_buttons:
            print(f"\nTrying to click button {btn_index}: '{text}' / '{aria_label}'")
            try:
                button = all_buttons.nth(btn_index)
                button.click()
                page.wait_for_timeout(3000)
                
                # Take screenshot after clicking
                menu_screenshot = os.path.join(download_dir, f"menu_{notebook_name.replace(' ', '_')[:20]}.png")
                page.screenshot(path=menu_screenshot)
                print(f"Menu screenshot: {menu_screenshot}")
                
                # Look for download option
                download_found = try_download(page, notebook_name, download_dir)
                if download_found:
                    return True
                    
                # If download not found, try to close menu and continue
                try:
                    page.keyboard.press('Escape')
                    page.wait_for_timeout(1000)
                except:
                    pass
                    
            except Exception as click_error:
                print(f"Failed to click button {btn_index}: {click_error}")
                continue
        
        # If no automatic success, try manual approach
        print("\n=== MANUAL APPROACH ===")
        print("Automatic detection failed. Please manually:")
        print("1. Look for the three dots (...) menu button")
        print("2. Click on it to open the menu")
        print("3. Click on 'Download'")
        
        try:
            with page.expect_download(timeout=30000) as download_info:
                input("Press Enter AFTER you've clicked Download...")
            
            download = download_info.value
            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
            download_path = os.path.join(download_dir, f"{safe_filename}.sql")
            download.save_as(download_path)
            print(f"✅ Manual download saved: {download_path}")
            return True
            
        except Exception as manual_error:
            print(f"Manual download failed: {manual_error}")
            return False
            
    except Exception as analysis_error:
        print(f"Button analysis failed: {analysis_error}")
        return False

def try_download(page, notebook_name, download_dir):
    """Try to find and click the download option"""
    print("Looking for Download option...")
    
    download_selectors = [
        'text="Download"',
        '*:has-text("Download")',
        'div:has-text("Download")',
        'span:has-text("Download")',
        'button:has-text("Download")',
        '[role="menuitem"]:has-text("Download")'
    ]
    
    for selector in download_selectors:
        try:
            elements = page.locator(selector)
            count = elements.count()
            print(f"Selector '{selector}': found {count} elements")
            
            if count > 0:
                print(f"Trying to download with selector: {selector}")
                
                with page.expect_download(timeout=10000) as download_info:
                    elements.first.click()
                
                download = download_info.value
                safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                download.save_as(download_path)
                print(f"✅ Downloaded: {download_path}")
                return True
                
        except Exception as download_error:
            print(f"Download attempt failed with {selector}: {download_error}")
            continue
    
    return False

if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
