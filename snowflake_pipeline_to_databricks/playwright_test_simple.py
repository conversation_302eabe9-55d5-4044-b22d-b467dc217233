from playwright.sync_api import sync_playwright, TimeoutError
import time
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake and login ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/notebooks"
    page.goto(start_url)
    print("Please log in to <PERSON><PERSON><PERSON> manually and navigate to the notebooks page...")
    
    # Wait for user to manually complete login
    input("Press Enter when you can see the notebooks list and are ready to continue...")
    
    print("Starting notebook download process...")
    
    # Take a screenshot to see current state
    page.screenshot(path=os.path.join(download_dir, "current_state.png"))
    print("Screenshot saved for debugging")
    
    # === Step 2: Try a different approach - look for clickable notebook names ===
    print("Looking for notebook links or clickable elements...")
    
    # Try to find clickable notebook elements
    clickable_selectors = [
        'a[href*="notebook"]',
        'button:has-text("Translate")',
        'button:has-text("MPRASANNAPUL")',
        'button:has-text("Reference")',
        '[role="button"]:has-text("Translate")',
        '[role="button"]:has-text("MPRASANNAPUL")',
        '[role="button"]:has-text("Reference")',
        'td:has-text("Translate")',
        'td:has-text("MPRASANNAPUL")',
        'td:has-text("Reference")'
    ]
    
    found_notebooks = []
    for selector in clickable_selectors:
        try:
            elements = page.locator(selector)
            count = elements.count()
            if count > 0:
                print(f"Found {count} elements with selector: {selector}")
                for i in range(count):
                    try:
                        text = elements.nth(i).inner_text()
                        if text and len(text.strip()) > 0:
                            found_notebooks.append((elements.nth(i), text.strip()))
                            print(f"  - Found notebook: {text.strip()}")
                    except:
                        pass
        except:
            continue
    
    if not found_notebooks:
        print("No clickable notebook elements found. Let's try a manual approach...")
        
        # Manual approach - let user click on notebooks
        notebook_names = ["Translate multilingual customer reviews", 
                         "MPRASANNAPUL 2025-07-25 16:54:13", 
                         "Reference cells and variables in Notebooks"]
        
        for notebook_name in notebook_names:
            print(f"\n=== Processing: {notebook_name} ===")
            print("Please manually click on the notebook in the browser window.")
            print("Wait for it to open, then press Enter to continue with download...")
            
            input(f"Press Enter when '{notebook_name}' is open and ready...")
            
            # Now try to download
            try:
                download_notebook(page, notebook_name, download_dir)
            except Exception as e:
                print(f"Failed to download {notebook_name}: {e}")
            
            # Go back to notebooks list
            print("Going back to notebooks list...")
            page.goto(start_url)
            page.wait_for_timeout(3000)
    else:
        # Try automated approach with found elements
        print(f"Found {len(found_notebooks)} notebooks to process")
        
        for element, notebook_name in found_notebooks:
            try:
                print(f"\n=== Processing: {notebook_name} ===")
                
                # Try to click the element
                try:
                    element.click(force=True)
                    print("Clicked notebook successfully")
                except:
                    print("Click failed, trying JavaScript click...")
                    element.evaluate('el => el.click()')
                
                # Wait for notebook to load
                page.wait_for_timeout(5000)
                
                # Try to download
                download_notebook(page, notebook_name, download_dir)
                
            except Exception as e:
                print(f"Failed to process {notebook_name}: {e}")
            
            finally:
                # Go back to notebooks list
                print("Returning to notebooks list...")
                page.goto(start_url)
                page.wait_for_timeout(3000)
    
    browser.close()
    print("✅ Process completed.")

def download_notebook(page, notebook_name, download_dir):
    """Function to handle the download process once a notebook is open"""
    print("Looking for download options...")
    
    # Take screenshot of current state
    page.screenshot(path=os.path.join(download_dir, f"notebook_{notebook_name.replace(' ', '_')}.png"))
    
    # Look for the three dots menu or download button
    menu_selectors = [
        '[aria-label*="More"]',
        'button[aria-label*="More"]',
        'button:has([data-icon="ellipsis"])',
        'button:has-text("⋮")',
        '[data-testid*="more"]',
        'button[title*="More"]',
        '.menu-trigger',
        '.dropdown-trigger'
    ]
    
    menu_found = False
    for selector in menu_selectors:
        try:
            menu_button = page.locator(selector)
            if menu_button.count() > 0 and menu_button.first.is_visible():
                print(f"Found menu button with selector: {selector}")
                menu_button.first.click()
                page.wait_for_timeout(1000)
                menu_found = True
                break
        except:
            continue
    
    if not menu_found:
        print("Could not find menu button. Looking for direct download button...")
        
        # Look for direct download button
        download_selectors = [
            'button:has-text("Download")',
            'a:has-text("Download")',
            '[title*="Download"]',
            '[aria-label*="Download"]'
        ]
        
        for selector in download_selectors:
            try:
                download_btn = page.locator(selector)
                if download_btn.count() > 0:
                    print(f"Found direct download button: {selector}")
                    with page.expect_download() as download_info:
                        download_btn.first.click()
                    
                    download = download_info.value
                    safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                    download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                    download.save_as(download_path)
                    print(f"✅ Downloaded: {download_path}")
                    return
            except:
                continue
        
        print("❌ Could not find download button")
        return
    
    # If menu was found, look for download option
    print("Looking for Download option in menu...")
    download_selectors = [
        'button:has-text("Download")',
        'div:has-text("Download")',
        '[role="menuitem"]:has-text("Download")',
        'li:has-text("Download")',
        'a:has-text("Download")'
    ]
    
    for selector in download_selectors:
        try:
            download_btn = page.locator(selector)
            if download_btn.count() > 0:
                print(f"Found download option: {selector}")
                with page.expect_download() as download_info:
                    download_btn.first.click()
                
                download = download_info.value
                safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                download.save_as(download_path)
                print(f"✅ Downloaded: {download_path}")
                return
        except Exception as e:
            print(f"Download attempt failed with {selector}: {e}")
            continue
    
    print("❌ Could not complete download")

with sync_playwright() as playwright:
    run(playwright)
