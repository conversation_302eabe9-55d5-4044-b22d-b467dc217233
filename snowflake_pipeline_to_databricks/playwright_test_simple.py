from playwright.sync_api import sync_playwright, TimeoutError
import time
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake and login ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/homepage"
    page.goto(start_url)
    print("Please log in to <PERSON>flake manually...")

    # Wait for user to manually complete login
    input("Press Enter when you're logged in and can see the homepage...")

    print("Navigating to Notebooks tab...")

    # === Step 2: Click on Notebooks tab ===
    try:
        # Look for the Notebooks tab in the "All projects" section
        notebooks_tab = page.locator('text="Notebooks"')
        if notebooks_tab.count() > 0:
            print("Found Notebooks tab, clicking...")
            notebooks_tab.click()
            page.wait_for_timeout(3000)
        else:
            print("Could not find Notebooks tab automatically")
            input("Please manually click on the 'Notebooks' tab and press Enter...")
    except Exception as e:
        print(f"Error clicking Notebooks tab: {e}")
        input("Please manually click on the 'Notebooks' tab and press Enter...")

    print("Starting notebook download process...")

    # Take a screenshot to see current state
    page.screenshot(path=os.path.join(download_dir, "notebooks_page.png"))
    print("Screenshot saved for debugging")
    
    # === Step 3: Find notebooks in the table ===
    print("Looking for notebooks in the table...")

    # Based on your screenshot, notebooks are in a table with TITLE, TYPE, VIEWED, UPDATED columns
    # Look for table rows containing notebooks
    notebook_selectors = [
        'tr:has-text("Notebook")',  # Rows that contain "Notebook" in TYPE column
        'tr:has-text("MPRASANNAPAUL")',
        'tr:has-text("Translate multilingual")',
        'tr:has-text("Reference cells")',
        # More generic selectors
        'tbody tr',
        'table tr',
        '[role="row"]'
    ]
    
    found_notebooks = []
    for selector in notebook_selectors:
        try:
            elements = page.locator(selector)
            count = elements.count()
            if count > 0:
                print(f"Found {count} elements with selector: {selector}")
                for i in range(count):
                    try:
                        text = elements.nth(i).inner_text()
                        if text and len(text.strip()) > 0:
                            found_notebooks.append((elements.nth(i), text.strip()))
                            print(f"  - Found notebook: {text.strip()}")
                    except:
                        pass
        except:
            continue
    
    if not found_notebooks:
        print("No clickable notebook elements found. Let's try a manual approach...")
        
        # Manual approach - let user click on notebooks
        notebook_names = ["Translate multilingual customer reviews", 
                         "MPRASANNAPUL 2025-07-25 16:54:13", 
                         "Reference cells and variables in Notebooks"]
        
        for notebook_name in notebook_names:
            print(f"\n=== Processing: {notebook_name} ===")
            print("Please manually click on the notebook in the browser window.")
            print("Wait for it to open, then press Enter to continue with download...")
            
            input(f"Press Enter when '{notebook_name}' is open and ready...")
            
            # Now try to download
            try:
                download_notebook(page, notebook_name, download_dir)
            except Exception as e:
                print(f"Failed to download {notebook_name}: {e}")
            
            # Go back to notebooks list
            print("Going back to notebooks list...")
            page.goto(start_url)
            page.wait_for_timeout(3000)
    else:
        # Try automated approach with found elements
        print(f"Found {len(found_notebooks)} notebooks to process")
        
        for element, notebook_name in found_notebooks:
            try:
                print(f"\n=== Processing: {notebook_name} ===")
                
                # Try to click the element
                try:
                    element.click(force=True)
                    print("Clicked notebook successfully")
                except:
                    print("Click failed, trying JavaScript click...")
                    element.evaluate('el => el.click()')
                
                # Wait for notebook to load
                page.wait_for_timeout(5000)
                
                # Try to download
                download_notebook(page, notebook_name, download_dir)
                
            except Exception as e:
                print(f"Failed to process {notebook_name}: {e}")
            
            finally:
                # Go back to notebooks list
                print("Returning to notebooks list...")
                page.goto(start_url)
                page.wait_for_timeout(3000)
    
    browser.close()
    print("✅ Process completed.")

def download_notebook(page, notebook_name, download_dir):
    """Function to handle the download process once a notebook is open"""
    print(f"Processing notebook: {notebook_name}")

    # Wait for notebook to fully load
    page.wait_for_timeout(3000)

    # Take screenshot of current state
    page.screenshot(path=os.path.join(download_dir, f"notebook_{notebook_name.replace(' ', '_')}.png"))
    print("Notebook screenshot saved")

    # Look for the three dots menu button (based on your screenshot)
    print("Looking for the three dots menu button...")
    
    # Look for the three dots menu button in the notebook interface
    # Based on your screenshot, it's in the top-right area of the notebook
    menu_selectors = [
        'button:has-text("...")',  # Horizontal three dots (most likely)
        'button[aria-label*="More"]',
        'button[title*="More"]',
        'button[aria-label*="menu"]',
        'button[aria-haspopup="menu"]',
        # Look in the notebook header/toolbar area
        '.notebook-header button',
        '.toolbar button',
        'header button:has-text("...")',
        # More generic fallbacks
        'button:has-text("⋯")',
        'button:has([data-icon="ellipsis"])',
        '[data-testid*="more"]',
        '[data-testid*="menu"]'
    ]

    menu_found = False
    print("Looking for menu button...")

    # Debug: Show all buttons on the page
    try:
        all_buttons = page.locator('button')
        button_count = all_buttons.count()
        print(f"Found {button_count} buttons on page")

        for i in range(min(10, button_count)):  # Check first 10 buttons
            try:
                button = all_buttons.nth(i)
                if button.is_visible():
                    text = button.inner_text() or ''
                    aria_label = button.get_attribute('aria-label') or ''
                    title = button.get_attribute('title') or ''
                    print(f"  Button {i}: text='{text}' aria-label='{aria_label}' title='{title}'")
            except:
                pass
    except:
        pass

    for selector in menu_selectors:
        try:
            menu_button = page.locator(selector)
            count = menu_button.count()
            if count > 0:
                print(f"Found {count} potential menu buttons with selector: {selector}")

                # Try each matching button
                for i in range(count):
                    try:
                        button = menu_button.nth(i)
                        if button.is_visible():
                            print(f"Trying menu button {i} with selector: {selector}")
                            button.click()
                            page.wait_for_timeout(1500)  # Wait longer for menu to appear
                            menu_found = True
                            break
                    except Exception as click_error:
                        print(f"Menu button click {i} failed: {click_error}")
                        continue

                if menu_found:
                    break
        except Exception as selector_error:
            print(f"Menu selector {selector} failed: {selector_error}")
            continue
    
    if not menu_found:
        print("Could not find menu button. Trying keyboard shortcut...")

        # Try keyboard shortcut to open context menu
        try:
            # Right-click might open a context menu
            page.click('body', button='right')
            page.wait_for_timeout(1000)

            # Or try keyboard shortcut
            page.keyboard.press('Shift+F10')  # Context menu shortcut
            page.wait_for_timeout(1000)

            # Check if download option appeared
            download_check = page.locator('*:has-text("Download"):visible')
            if download_check.count() > 0:
                print("Found download option via keyboard shortcut")
                menu_found = True

        except:
            pass

    if not menu_found:
        print("Could not find menu button. Looking for direct download button...")
        
        # Look for direct download button
        download_selectors = [
            'button:has-text("Download")',
            'a:has-text("Download")',
            '[title*="Download"]',
            '[aria-label*="Download"]'
        ]
        
        for selector in download_selectors:
            try:
                download_btn = page.locator(selector)
                if download_btn.count() > 0:
                    print(f"Found direct download button: {selector}")
                    with page.expect_download() as download_info:
                        download_btn.first.click()
                    
                    download = download_info.value
                    safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                    download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                    download.save_as(download_path)
                    print(f"✅ Downloaded: {download_path}")
                    return
            except:
                continue
        
        print("❌ Could not find download button")
        return
    
    # If menu was found, look for download option
    print("Looking for Download option in menu...")

    # Wait a bit more for menu to fully appear
    page.wait_for_timeout(1500)

    # Take a screenshot to see the menu
    page.screenshot(path=os.path.join(download_dir, f"menu_{notebook_name.replace(' ', '_')}.png"))
    print("Menu screenshot saved for debugging")

    # Look for the Download option in the dropdown menu
    # Based on your screenshot: Copy relative path, Copy full path, Download, Remove
    download_selectors = [
        'text="Download"',  # Exact text match (most reliable)
        '*:has-text("Download")',  # Any element containing "Download"
        'div:has-text("Download")',
        'span:has-text("Download")',
        'button:has-text("Download")',
        '[role="menuitem"]:has-text("Download")',
        'li:has-text("Download")',
        # Try different menu item structures
        '.menu-item:has-text("Download")',
        '.dropdown-item:has-text("Download")',
        '.context-menu-item:has-text("Download")',
        '[data-testid*="download"]',
        '[aria-label*="Download"]'
    ]

    # Debug: List all visible elements that might be the download option
    print("Debugging menu contents...")
    try:
        # Look for any visible text elements
        all_visible_elements = page.locator('*:visible')
        count = all_visible_elements.count()
        print(f"Found {count} visible elements")

        # Look specifically for elements containing "Download"
        download_elements = page.locator('*:has-text("Download"):visible')
        download_count = download_elements.count()
        print(f"Found {download_count} visible elements containing 'Download'")

        for i in range(min(5, download_count)):  # Check first 5 matches
            try:
                element = download_elements.nth(i)
                text = element.inner_text()
                tag = element.evaluate('el => el.tagName')
                classes = element.get_attribute('class') or ''
                print(f"  Download element {i}: <{tag}> class='{classes}' text='{text}'")
            except:
                pass

    except Exception as debug_error:
        print(f"Debug error: {debug_error}")

    # Try each selector
    download_successful = False
    for selector in download_selectors:
        try:
            download_btn = page.locator(selector)
            count = download_btn.count()
            if count > 0:
                print(f"Found {count} download elements with selector: {selector}")

                # Try each matching element
                for i in range(count):
                    try:
                        element = download_btn.nth(i)
                        if element.is_visible():
                            print(f"Trying to click download element {i} with selector: {selector}")

                            with page.expect_download(timeout=15000) as download_info:
                                element.click()

                            download = download_info.value
                            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                            download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                            download.save_as(download_path)
                            print(f"✅ Downloaded: {download_path}")
                            download_successful = True
                            break
                    except Exception as click_error:
                        print(f"Click attempt {i} failed: {click_error}")
                        continue

                if download_successful:
                    break

        except Exception as e:
            print(f"Selector {selector} failed: {e}")
            continue

    if not download_successful:
        print("❌ Could not find or click download option")
        # Try one more approach - click on any element that contains exactly "Download"
        try:
            print("Trying exact text match for 'Download'...")
            exact_download = page.locator('text="Download"')
            if exact_download.count() > 0:
                print("Found exact 'Download' text, attempting click...")
                with page.expect_download(timeout=15000) as download_info:
                    exact_download.first.click()

                download = download_info.value
                safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                download.save_as(download_path)
                print(f"✅ Downloaded with exact text match: {download_path}")
                return
        except Exception as exact_error:
            print(f"Exact text match failed: {exact_error}")

    if download_successful:
        return
    
    print("❌ Could not complete download")

with sync_playwright() as playwright:
    run(playwright)
