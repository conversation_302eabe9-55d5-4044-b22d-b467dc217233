from playwright.sync_api import sync_playwright
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/notebooks"
    page.goto(start_url)
    
    print("Please log in and navigate to a notebook...")
    input("Press Enter when you have a notebook open and can see the three dots menu...")
    
    # Take screenshot to see current state
    page.screenshot(path=os.path.join(download_dir, "current_notebook.png"))
    print("Screenshot saved")
    
    # Try to find and click the horizontal three dots menu
    print("Looking for the horizontal three dots menu (...)...")
    
    # More specific selectors based on your screenshot
    menu_selectors = [
        'button:has-text("...")',
        'button[aria-label*="More"]',
        'button[title*="More"]',
        '[role="button"]:has-text("...")',
        # Look for buttons that might contain the dots
        'button:has([data-icon*="dot"])',
        'button:has([data-icon*="ellipsis"])',
        'button:has([data-icon*="more"])',
        # Try CSS selectors that might match the menu button
        '.menu-button',
        '.more-button',
        '.action-button',
        # Look for any button near the notebook title area
        'header button',
        '.notebook-header button',
        '.title-bar button'
    ]
    
    menu_clicked = False
    
    # Debug: Show all buttons and their properties
    print("\n=== DEBUGGING ALL BUTTONS ===")
    try:
        all_buttons = page.locator('button:visible')
        count = all_buttons.count()
        print(f"Found {count} visible buttons")
        
        for i in range(min(15, count)):
            try:
                button = all_buttons.nth(i)
                text = button.inner_text().strip()
                aria_label = button.get_attribute('aria-label') or ''
                title = button.get_attribute('title') or ''
                class_name = button.get_attribute('class') or ''
                
                print(f"Button {i}:")
                print(f"  Text: '{text}'")
                print(f"  Aria-label: '{aria_label}'")
                print(f"  Title: '{title}'")
                print(f"  Class: '{class_name}'")
                print(f"  ---")
                
                # If this looks like a menu button, try clicking it
                if ('...' in text or 
                    'more' in aria_label.lower() or 
                    'more' in title.lower() or
                    'menu' in aria_label.lower() or
                    'action' in class_name.lower()):
                    
                    print(f"*** This looks like a menu button! Trying to click it...")
                    try:
                        button.click()
                        page.wait_for_timeout(2000)
                        menu_clicked = True
                        print("*** Menu button clicked successfully!")
                        break
                    except Exception as click_error:
                        print(f"*** Click failed: {click_error}")
                        
            except Exception as button_error:
                print(f"Error examining button {i}: {button_error}")
                
    except Exception as debug_error:
        print(f"Debug error: {debug_error}")
    
    print("=== END BUTTON DEBUGGING ===\n")
    
    # If we haven't found the menu yet, try the selectors
    if not menu_clicked:
        print("Trying specific selectors...")
        for selector in menu_selectors:
            try:
                buttons = page.locator(selector)
                count = buttons.count()
                if count > 0:
                    print(f"Found {count} buttons with selector: {selector}")
                    for i in range(count):
                        try:
                            button = buttons.nth(i)
                            if button.is_visible():
                                print(f"Clicking button {i} with selector: {selector}")
                                button.click()
                                page.wait_for_timeout(2000)
                                menu_clicked = True
                                break
                        except:
                            continue
                    if menu_clicked:
                        break
            except:
                continue
    
    if not menu_clicked:
        print("❌ Could not find or click the menu button")
        print("Please manually click the three dots menu (...) now")
        input("Press Enter after you've clicked the menu and can see the dropdown...")
    
    # Now look for the Download option
    print("Looking for Download option in the menu...")
    
    # Take another screenshot to see the menu
    page.screenshot(path=os.path.join(download_dir, "menu_open.png"))
    print("Menu screenshot saved")
    
    # Debug: Show all visible text elements
    print("\n=== DEBUGGING MENU CONTENTS ===")
    try:
        # Look for all visible elements containing text
        text_elements = page.locator('*:visible')
        count = text_elements.count()
        print(f"Checking {min(50, count)} visible elements for 'Download'...")
        
        download_candidates = []
        
        for i in range(min(50, count)):
            try:
                element = text_elements.nth(i)
                text = element.inner_text().strip()
                if text and 'download' in text.lower():
                    tag = element.evaluate('el => el.tagName')
                    classes = element.get_attribute('class') or ''
                    role = element.get_attribute('role') or ''
                    
                    print(f"Download candidate {len(download_candidates)}:")
                    print(f"  Tag: {tag}")
                    print(f"  Text: '{text}'")
                    print(f"  Class: '{classes}'")
                    print(f"  Role: '{role}'")
                    print(f"  ---")
                    
                    download_candidates.append(element)
                    
            except:
                continue
                
        print(f"Found {len(download_candidates)} download candidates")
        
    except Exception as menu_debug_error:
        print(f"Menu debug error: {menu_debug_error}")
    
    print("=== END MENU DEBUGGING ===\n")
    
    # Try to click the Download option
    download_selectors = [
        'text="Download"',  # Exact text match
        '*:has-text("Download")',
        'button:has-text("Download")',
        'div:has-text("Download")',
        'span:has-text("Download")',
        '[role="menuitem"]:has-text("Download")',
        'li:has-text("Download")',
        'a:has-text("Download")'
    ]
    
    download_success = False
    
    for selector in download_selectors:
        try:
            download_elements = page.locator(selector)
            count = download_elements.count()
            if count > 0:
                print(f"Found {count} download elements with selector: {selector}")
                
                for i in range(count):
                    try:
                        element = download_elements.nth(i)
                        if element.is_visible():
                            print(f"Trying to click download element {i}")
                            
                            # Start expecting download before clicking
                            with page.expect_download(timeout=20000) as download_info:
                                element.click()
                            
                            # Handle the download
                            download = download_info.value
                            download_path = os.path.join(download_dir, f"notebook_download_{int(time.time())}.sql")
                            download.save_as(download_path)
                            print(f"✅ Successfully downloaded to: {download_path}")
                            download_success = True
                            break
                            
                    except Exception as download_click_error:
                        print(f"Download click {i} failed: {download_click_error}")
                        continue
                
                if download_success:
                    break
                    
        except Exception as download_selector_error:
            print(f"Download selector {selector} failed: {download_selector_error}")
            continue
    
    if not download_success:
        print("❌ Automatic download failed")
        print("Please manually click the 'Download' option now")
        
        try:
            with page.expect_download(timeout=30000) as download_info:
                input("Press Enter AFTER you've clicked Download...")
            
            download = download_info.value
            download_path = os.path.join(download_dir, f"manual_download_{int(time.time())}.sql")
            download.save_as(download_path)
            print(f"✅ Manual download saved to: {download_path}")
            
        except Exception as manual_error:
            print(f"Manual download also failed: {manual_error}")
    
    input("Press Enter to close browser...")
    browser.close()

if __name__ == "__main__":
    import time
    with sync_playwright() as playwright:
        run(playwright)
