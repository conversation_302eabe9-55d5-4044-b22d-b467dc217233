from playwright.sync_api import sync_playwright, TimeoutError
import time
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def debug_page_elements(page, description=""):
    """Helper function to debug what elements are available on the page"""
    print(f"\n=== DEBUG: {description} ===")
    try:
        # Print page title and URL
        print(f"Page URL: {page.url}")
        print(f"Page Title: {page.title()}")

        # Take a screenshot for debugging
        screenshot_path = os.path.join(download_dir, f"debug_{description.replace(' ', '_')}.png")
        page.screenshot(path=screenshot_path)
        print(f"Screenshot saved: {screenshot_path}")

        # Look for common button/menu elements
        common_selectors = [
            'button',
            '[role="button"]',
            '[aria-label*="More"]',
            '[data-testid*="more"]',
            'button:has-text("Download")',
            'div:has-text("Download")',
            '*:has-text("Download")',
            '[title*="Download"]'
        ]

        for selector in common_selectors:
            try:
                elements = page.locator(selector)
                count = elements.count()
                if count > 0:
                    print(f"Found {count} elements with selector: {selector}")
                    # Print first few element texts
                    for i in range(min(3, count)):
                        try:
                            text = elements.nth(i).inner_text()
                            if text.strip():
                                print(f"  - Element {i}: '{text[:50]}'")
                        except:
                            pass
            except:
                pass
    except Exception as e:
        print(f"Debug error: {e}")
    print("=== END DEBUG ===\n")

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake and login ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/notebooks"
    page.goto(start_url)
    print("Please log in to Snowflake manually...")

    # Wait for successful login by checking for specific elements that only appear after login
    print("Waiting for login to complete...")

    # Wait for either the notebooks page to load OR check for login-specific elements
    login_successful = False
    max_attempts = 60  # 60 attempts = 2 minutes
    attempt = 0

    while not login_successful and attempt < max_attempts:
        try:
            # Check if we're actually on the notebooks page with content
            current_url = page.url
            print(f"Current URL: {current_url}")

            # Check for elements that indicate successful login and notebooks page load
            if "/#/notebooks" in current_url:
                # Look for notebook-specific elements
                notebook_indicators = [
                    '[data-testid^="glide-cell"]',
                    'table',
                    'tr',
                    'div:has-text("Notebooks")',
                    'div:has-text("TITLE")',
                    'div:has-text("LOCATION")'
                ]

                for indicator in notebook_indicators:
                    try:
                        if page.locator(indicator).count() > 0:
                            print(f"Found notebook page indicator: {indicator}")
                            login_successful = True
                            break
                    except:
                        continue

                if login_successful:
                    break

            # If not successful, wait and try again
            page.wait_for_timeout(2000)  # Wait 2 seconds
            attempt += 1

        except Exception as e:
            print(f"Waiting for login... (attempt {attempt + 1}/{max_attempts})")
            page.wait_for_timeout(2000)
            attempt += 1

    if not login_successful:
        print("❌ Login timeout or failed to detect notebooks page. Please check your login.")
        browser.close()
        return

    print("✅ Login detected and notebooks page loaded.")

    # Give the UI time to load the notebook list
    print("Waiting for the notebook list to load...")
    page.wait_for_timeout(5000)

    # Debug: Check what's on the page after login
    debug_page_elements(page, "After successful login")

    # === Step 2: Find all notebook list items ===
    # Wait for notebooks to load and try different selectors
    print("Looking for notebook list items...")
    page.wait_for_timeout(3000)

    # Try multiple selectors to find notebook items
    notebook_selectors = [
        '[data-testid^="glide-cell-0-"]',
        '[data-testid^="glide-cell"]',
        'tr[data-testid*="row"]',
        'div[role="row"]',
        'tr:has-text("Translate")',
        'tr:has-text("MPRASANNAPUL")',
        'tr:has-text("Reference")',
        'tbody tr',
        'table tr',
        '[role="gridcell"]',
        'div:has-text("Translate multilingual")',
        'div:has-text("MPRASANNAPUL")',
        'div:has-text("Reference cells")'
    ]

    notebook_list_items = None
    for selector in notebook_selectors:
        try:
            items = page.locator(selector)
            count = items.count()
            if count > 0:
                print(f"Found {count} notebooks using selector: {selector}")
                notebook_list_items = items
                break
        except:
            continue

    if notebook_list_items is None or notebook_list_items.count() == 0:
        print("No notebooks found with any selector.")
        print("Let's debug what's actually on the page...")

        # More comprehensive debugging
        try:
            # Get page HTML for analysis
            html_content = page.content()
            print(f"Page HTML length: {len(html_content)}")

            # Look for any table-like structures
            tables = page.locator('table').count()
            print(f"Found {tables} table elements")

            # Look for any row-like structures
            rows = page.locator('tr').count()
            print(f"Found {rows} tr elements")

            # Look for divs that might contain notebook names
            divs_with_text = page.locator('div').count()
            print(f"Found {divs_with_text} div elements")

            # Try to find any clickable elements
            clickable = page.locator('button, a, [role="button"], [onclick], [data-testid]').count()
            print(f"Found {clickable} potentially clickable elements")

            # Look for specific text patterns
            text_patterns = ["Translate", "MPRASANNAPUL", "Reference", "notebook", "Notebook"]
            for pattern in text_patterns:
                elements = page.locator(f'*:has-text("{pattern}")').count()
                if elements > 0:
                    print(f"Found {elements} elements containing text '{pattern}'")

        except Exception as debug_error:
            print(f"Debug error: {debug_error}")

        # Give user a chance to manually navigate
        print("\n" + "="*50)
        print("MANUAL INTERVENTION REQUIRED")
        print("="*50)
        print("The script couldn't automatically find notebooks.")
        print("Please manually navigate to the notebooks page in the browser window.")
        print("Make sure you can see the list of notebooks, then press Enter to continue...")
        print("Or type 'exit' to quit the script.")
        print("="*50)

        user_input = input("Press Enter when ready (or 'exit' to quit): ").strip().lower()
        if user_input == 'exit':
            print("Exiting as requested.")
            browser.close()
            return

        # Try again after manual navigation
        print("Trying to find notebooks again...")
        page.wait_for_timeout(2000)

        for selector in notebook_selectors:
            try:
                items = page.locator(selector)
                count = items.count()
                if count > 0:
                    print(f"Found {count} notebooks using selector: {selector}")
                    notebook_list_items = items
                    break
            except:
                continue

        if notebook_list_items is None or notebook_list_items.count() == 0:
            print("Still no notebooks found. Exiting.")
            browser.close()
            return

    count = notebook_list_items.count()
    print(f"Total notebooks to process: {count}")

    # === Step 3: Loop through notebooks, enter, and download ===
    for i in range(count):
        notebook_name = ""
        try:
            # Re-fetch the locator in each iteration to avoid stale elements
            current_notebook_item = notebook_list_items.nth(i)

            # Try to get notebook name from different possible locations
            try:
                notebook_name = current_notebook_item.locator('td').first.inner_text()
            except:
                try:
                    notebook_name = current_notebook_item.inner_text().split('\n')[0]
                except:
                    notebook_name = f"notebook_{i+1}"

            print(f"--- Processing notebook #{i+1}: {notebook_name} ---")

            # 1. Click the notebook to open it
            print("Clicking notebook to open...")
            current_notebook_item.click()

            # 2. Wait for the notebook page to load
            print("Waiting for notebook page to load...")
            page.wait_for_timeout(5000)

            # Debug: Check what's available on the page
            debug_page_elements(page, f"After opening notebook '{notebook_name}'")

            # 3. Look for the three dots menu button (More options)
            print("Looking for the 'More options' menu...")

            # Try different selectors for the three dots menu
            menu_selectors = [
                '[aria-label*="More notebook actions"]',
                'button[aria-label*="More"]',
                'button:has([data-icon="ellipsis"])',
                'button:has-text("⋮")',
                '[data-testid*="more"]',
                'button[title*="More"]'
            ]

            menu_button = None
            for selector in menu_selectors:
                try:
                    button = page.locator(selector)
                    if button.count() > 0:
                        print(f"Found menu button with selector: {selector}")
                        menu_button = button.first
                        break
                except:
                    continue

            if menu_button is None:
                print("Could not find menu button, trying iframe context...")
                # Try to find iframe and look inside it
                try:
                    iframe_selector = '[data-testid="notebooks-results-iframe-wrapper"]'
                    if page.locator(iframe_selector).count() > 0:
                        frame = page.frame_locator(iframe_selector)
                        for selector in menu_selectors:
                            try:
                                button = frame.locator(selector)
                                if button.count() > 0:
                                    print(f"Found menu button in iframe with selector: {selector}")
                                    menu_button = button.first
                                    break
                            except:
                                continue
                except:
                    pass

            if menu_button is None:
                print("❌ Could not find the 'More options' menu button")
                continue

            # 4. Click the menu button
            print("Clicking the 'More options' menu...")
            menu_button.click()
            page.wait_for_timeout(1000)  # Wait for menu to appear

            # Debug: Check what's available after clicking menu
            debug_page_elements(page, "After clicking More options menu")

            # 5. Look for and click the Download option
            print("Looking for Download option...")

            download_selectors = [
                'button:has-text("Download")',
                'div:has-text("Download")',
                '[role="menuitem"]:has-text("Download")',
                'li:has-text("Download")',
                'a:has-text("Download")'
            ]

            download_button = None
            for selector in download_selectors:
                try:
                    button = page.locator(selector)
                    if button.count() > 0:
                        print(f"Found download button with selector: {selector}")
                        download_button = button.first
                        break
                except:
                    continue

            if download_button is None:
                print("❌ Could not find Download button, trying alternative approach...")
                # Try clicking any element that contains "Download" text
                try:
                    page.locator('*:has-text("Download")').first.click()
                    print("Found Download using text search")
                except:
                    print("❌ All download attempts failed")
                    continue

            # 6. Click download and handle the file
            print("Clicking Download...")
            try:
                with page.expect_download(timeout=30000) as download_info:
                    if download_button:
                        download_button.click()
                    else:
                        # Already clicked in the fallback above
                        pass
            except Exception as download_error:
                print(f"❌ Download failed: {download_error}")
                continue

            download = download_info.value
            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
            if not safe_filename:
                safe_filename = f"notebook_{i+1}"

            download_path = os.path.join(download_dir, f"{safe_filename}.sql")

            download.save_as(download_path)
            print(f"✅ Successfully downloaded '{notebook_name}' to {download_path}")

        except Exception as e:
            print(f"❌ Failed to process notebook '{notebook_name}': {e}")

        finally:
            # ALWAYS go back to the notebook list to ensure a clean state
            print("Returning to the notebook list...")
            page.goto(start_url)
            page.wait_for_url("**/#/notebooks", timeout=60000)
            page.wait_for_timeout(5000) # Wait for the list to reload
            print("-----------------------------------------")

    # === Step 4: Done ===
    browser.close()
    print("✅ All notebooks processed.")

with sync_playwright() as playwright:
    run(playwright)
