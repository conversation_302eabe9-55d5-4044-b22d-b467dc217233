from playwright.sync_api import sync_playwright, TimeoutError
import time
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake and login ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/notebooks"
    page.goto(start_url)
    print("Please log in to <PERSON>flake manually...")
    # Wait for the URL to confirm that the login was successful and we are on the correct page.
    page.wait_for_url("**/#/notebooks", timeout=120000)
    print("Login detected and notebooks page loaded.")

    # Give the UI time to load the notebook list
    print("Waiting for the notebook list to load...")
    page.wait_for_timeout(5000)

    # === Step 2: Find all notebook list items ===
    notebook_list_items = page.locator('[data-testid^="glide-cell-0-"]')
    count = notebook_list_items.count()
    print(f"Found {count} notebooks to process.")

    if count == 0:
        print("No notebooks found. Exiting.")
        browser.close()
        return

    # === Step 3: Loop through notebooks, enter, and download ===
    for i in range(count):
        notebook_name = ""
        try:
            # Re-fetch the locator in each iteration to avoid stale elements
            current_notebook_item = page.locator('[data-testid^="glide-cell-0-"]').nth(i)
            notebook_name = current_notebook_item.inner_text()
            print(f"--- Processing notebook #{i+1}: {notebook_name} ---")

            # 1. Use JavaScript to click the element, bypassing visibility checks
            print("Entering notebook using direct JavaScript click...")
            current_notebook_item.evaluate('element => element.click()')
            
            # 2. Wait for the notebook's iframe to be attached
            print("Waiting for notebook iframe to be attached...")
            iframe_selector = '[data-testid="notebooks-results-iframe-wrapper"]'
            page.locator(iframe_selector).wait_for(state='attached', timeout=30000)
            
            # 3. Get the frame and wait for its content to fully load
            print("Waiting for iframe content to load...")
            frame = page.frame_locator(iframe_selector).frame
            frame.wait_for_load_state('load', timeout=30000)
            print("Notebook iframe and its content are fully loaded.")

            # 4. Click the 'More options' menu inside the notebook frame
            print("Opening the 'More options' menu...")
            frame.locator('[aria-label*="More notebook actions"]').click(timeout=10000)
            page.wait_for_timeout(500) # Wait for menu to open

            # 5. Click the 'Download' button
            print("Downloading notebook...")
            with page.expect_download() as download_info:
                page.locator('button:has-text("Download")').click()
            
            download = download_info.value
            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
            download_path = os.path.join(download_dir, f"{safe_filename}.sql")
            
            download.save_as(download_path)
            print(f"Successfully downloaded '{notebook_name}' to {download_path}")

        except Exception as e:
            print(f"❌ Failed to process notebook '{notebook_name}': {e}")
        
        finally:
            # ALWAYS go back to the notebook list to ensure a clean state
            print("Returning to the notebook list...")
            page.goto(start_url)
            page.wait_for_url("**/#/notebooks", timeout=60000)
            page.wait_for_timeout(5000) # Wait for the list to reload
            print("-----------------------------------------")

    # === Step 4: Done ===
    browser.close()
    print("✅ All notebooks processed.")

with sync_playwright() as playwright:
    run(playwright)
