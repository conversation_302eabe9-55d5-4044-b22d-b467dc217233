from playwright.sync_api import sync_playwright
import os

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/notebooks"
    page.goto(start_url)
    
    print("="*60)
    print("MANUAL SNOWFLAKE NOTEBOOK DOWNLOADER")
    print("="*60)
    print("1. Please log in to <PERSON>flake in the browser window")
    print("2. Navigate to the notebooks page if not already there")
    print("3. You should see a list of notebooks")
    print("="*60)
    
    input("Press Enter when you're ready to start downloading notebooks...")
    
    # List of notebooks to download (you can modify this)
    notebooks_to_download = [
        "Translate multilingual customer reviews",
        "MPRASANNAPUL 2025-07-25 16:54:13", 
        "Reference cells and variables in Notebooks"
    ]
    
    for i, notebook_name in enumerate(notebooks_to_download, 1):
        print(f"\n{'='*50}")
        print(f"NOTEBOOK {i}/{len(notebooks_to_download)}: {notebook_name}")
        print(f"{'='*50}")
        
        print("INSTRUCTIONS:")
        print("1. In the browser, click on the notebook to open it")
        print("2. Wait for the notebook to fully load")
        print("3. Look for the three dots menu (⋮) or More options button")
        print("4. Click on it to open the menu")
        print("5. Click on 'Download' option")
        print("6. The download should start automatically")
        print("\nDO NOT press Enter until you see the download has started!")
        
        input(f"Press Enter when you've started the download for '{notebook_name}'...")
        
        # Wait for download to complete
        try:
            print("Waiting for download to complete...")
            with page.expect_download(timeout=30000) as download_info:
                # The download should already be in progress from manual action
                pass
            
            download = download_info.value
            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
            download_path = os.path.join(download_dir, f"{safe_filename}.sql")
            
            download.save_as(download_path)
            print(f"✅ Successfully saved: {download_path}")
            
        except Exception as e:
            print(f"❌ Download failed or timed out: {e}")
            print("You can try again manually or skip this notebook")
            
            retry = input("Do you want to retry this notebook? (y/n): ").lower().strip()
            if retry == 'y':
                try:
                    print("Waiting for download (retry)...")
                    with page.expect_download(timeout=30000) as download_info:
                        pass
                    
                    download = download_info.value
                    safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                    download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                    
                    download.save_as(download_path)
                    print(f"✅ Successfully saved: {download_path}")
                    
                except Exception as retry_error:
                    print(f"❌ Retry also failed: {retry_error}")
        
        # Ask user to go back to notebooks list
        if i < len(notebooks_to_download):  # Not the last notebook
            print("\nPlease navigate back to the notebooks list in the browser")
            input("Press Enter when you're back at the notebooks list...")
    
    print("\n" + "="*60)
    print("DOWNLOAD PROCESS COMPLETED!")
    print("="*60)
    print(f"Downloaded files should be in: {download_dir}")
    print("Check the folder for your notebook files.")
    
    input("Press Enter to close the browser...")
    browser.close()

# Additional helper function to list downloaded files
def list_downloaded_files():
    print(f"\nFiles in {download_dir}:")
    try:
        files = os.listdir(download_dir)
        if files:
            for file in files:
                file_path = os.path.join(download_dir, file)
                size = os.path.getsize(file_path)
                print(f"  - {file} ({size} bytes)")
        else:
            print("  No files found")
    except Exception as e:
        print(f"  Error listing files: {e}")

if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
    
    # Show downloaded files
    list_downloaded_files()
