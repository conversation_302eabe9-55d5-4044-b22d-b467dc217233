from playwright.sync_api import sync_playwright
import os
import time

# Setup
DOWNLOAD_PATH = "/home/<USER>/playwright_script"
SNOWFLAKE_URL = "https://app.snowflake.com/lcdxius/rb10809/#/projects"

if not os.path.exists(DOWNLOAD_PATH):
    os.makedirs(DOWNLOAD_PATH)

def get_notebooks(page):
    print("Looking for notebooks...")
    time.sleep(2)
    
    # Find notebook links
    notebooks = page.locator('a[href*="/notebooks/"]').all()
    
    valid_notebooks = []
    for nb in notebooks:
        if nb.is_visible():
            name = nb.get_attribute('aria-label')
            if name and len(name) > 3:
                valid_notebooks.append(nb)
    
    print(f"Found {len(valid_notebooks)} notebooks")
    return valid_notebooks

def download_notebook(page, notebook_link):
    try:
        name = notebook_link.get_attribute('aria-label')
        print(f"Processing: {name}")

        # Click notebook
        notebook_link.click()
        time.sleep(3)

        # Look for menu button using working selectors
        menu_selectors = [
            'button[aria-label="More notebook actions"]',
            'div[role="button"][aria-label="More notebook actions"]',
            'button:has-text("...")',
            'button >> svg[aria-label="Ellipsis"]'
        ]

        menu_btn = None
        for selector in menu_selectors:
            try:
                elements = page.locator(selector).all()
                for element in elements:
                    if element.is_visible():
                        # Check if it's in header area
                        box = element.bounding_box()
                        if box and box['y'] < 300:
                            menu_btn = element
                            break
                if menu_btn:
                    break
            except:
                continue

        if not menu_btn:
            print(f"Menu button not found for {name}")
            return False

        # Click menu button
        menu_btn.scroll_into_view_if_needed()
        time.sleep(1)
        menu_btn.click()
        time.sleep(3)

        # Find export option
        export_selectors = [
            'text="Export"',
            'button:has-text("Export")',
            'div:has-text("Export")',
            'li:has-text("Export")',
            'span:has-text("Export")'
        ]

        export_clicked = False
        for selector in export_selectors:
            try:
                export_btn = page.locator(selector).first
                if export_btn.is_visible():
                    export_btn.click()
                    export_clicked = True
                    time.sleep(3)
                    break
            except:
                continue

        if not export_clicked:
            print(f"Export option not found for {name}")
            return False

        # Look for final export button in dialog and handle download
        final_selectors = [
            'div[role="button"] >> text="Export"',
            'div[role="button"]:has-text("Export"):not(:has-text("Cancel"))',
            'button:has-text("Export"):not(:has-text("Cancel"))'
        ]

        for selector in final_selectors:
            try:
                final_btn = page.locator(selector).first
                if final_btn.is_visible():
                    button_text = final_btn.inner_text().lower()
                    if 'export' in button_text and 'cancel' not in button_text:
                        # Set up download handling
                        with page.expect_download() as download_info:
                            final_btn.click()

                        # Save the download
                        download = download_info.value
                        safe_name = "".join(c for c in name if c.isalnum() or c in (' ', '_', '-')).strip()
                        download_path = os.path.join(DOWNLOAD_PATH, f"{safe_name}.sql")
                        download.save_as(download_path)

                        print(f"Downloaded: {name}")
                        time.sleep(2)
                        return True
            except Exception as e:
                print(f"Download error: {e}")
                continue

        print(f"Final export button not found for {name}")
        return False

    except Exception as e:
        print(f"Error downloading notebook: {e}")
        return False

def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(accept_downloads=True)
        page = context.new_page()
        
        # Navigate to Snowflake
        page.goto(SNOWFLAKE_URL)
        print("Please log in to Snowflake...")
        input("Press Enter when logged in...")
        
        # Click Notebooks tab
        try:
            notebooks_tab = page.locator('text="Notebooks"').first
            notebooks_tab.click()
            time.sleep(3)
            print("Clicked Notebooks tab")
        except:
            print("Please manually click Notebooks tab")
            input("Press Enter when on Notebooks page...")
        
        # Get all notebooks
        notebooks = get_notebooks(page)
        
        if not notebooks:
            print("No notebooks found")
            return
        
        # Download each notebook
        downloaded = 0
        for i, notebook in enumerate(notebooks):
            print(f"\nProcessing notebook {i+1}/{len(notebooks)}")
            
            if download_notebook(page, notebook):
                downloaded += 1
            
            # Go back to notebooks list
            try:
                page.goto(SNOWFLAKE_URL)
                time.sleep(3)
                notebooks_tab = page.locator('text="Notebooks"').first
                notebooks_tab.click()
                time.sleep(2)
            except Exception as e:
                print(f"Error navigating back: {e}")
                break
        
        print(f"\nCompleted! Downloaded {downloaded} notebooks")
        
        # List files
        try:
            files = os.listdir(DOWNLOAD_PATH)
            print(f"Files in {DOWNLOAD_PATH}:")
            for f in files:
                print(f"  - {f}")
        except:
            pass
        
        browser.close()

if __name__ == "__main__":
    main()
