from playwright.sync_api import sync_playwright
import os
import time

# Setup
DOWNLOAD_PATH = "/home/<USER>/playwright_script"
SNOWFLAKE_URL = "https://app.snowflake.com/lcdxius/rb10809/#/projects"

if not os.path.exists(DOWNLOAD_PATH):
    os.makedirs(DOWNLOAD_PATH)

def get_notebooks(page):
    print("Looking for notebooks...")
    time.sleep(2)
    
    # Find notebook links
    notebooks = page.locator('a[href*="/notebooks/"]').all()
    
    valid_notebooks = []
    for nb in notebooks:
        if nb.is_visible():
            name = nb.get_attribute('aria-label')
            if name and len(name) > 3:
                valid_notebooks.append(nb)
    
    print(f"Found {len(valid_notebooks)} notebooks")
    return valid_notebooks

def download_notebook(page, notebook_link):
    try:
        name = notebook_link.get_attribute('aria-label')
        print(f"Processing: {name}")
        
        # Click notebook
        notebook_link.click()
        time.sleep(4)
        
        # Look for menu button
        menu_button = None
        selectors = [
            'button[aria-label*="More"]',
            'button:has-text("...")',
            'button[aria-label*="actions"]'
        ]
        
        for selector in selectors:
            try:
                btn = page.locator(selector).first
                if btn.is_visible():
                    menu_button = btn
                    break
            except:
                continue
        
        if not menu_button:
            print(f"Menu button not found for {name}")
            return False
        
        # Click menu
        menu_button.click()
        time.sleep(2)
        
        # Find export option
        export_options = ['text="Export"', 'text="Download"']
        export_clicked = False
        
        for option in export_options:
            try:
                export_btn = page.locator(option).first
                if export_btn.is_visible():
                    export_btn.click()
                    export_clicked = True
                    break
            except:
                continue
        
        if not export_clicked:
            print(f"Export option not found for {name}")
            return False
        
        time.sleep(2)
        
        # Look for final export button in dialog
        final_export = page.locator('div[role="button"]:has-text("Export")').first
        if final_export.is_visible():
            final_export.click()
            print(f"Downloaded: {name}")
            time.sleep(3)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error downloading notebook: {e}")
        return False

def main():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(accept_downloads=True)
        page = context.new_page()
        
        # Navigate to Snowflake
        page.goto(SNOWFLAKE_URL)
        print("Please log in to Snowflake...")
        input("Press Enter when logged in...")
        
        # Click Notebooks tab
        try:
            notebooks_tab = page.locator('text="Notebooks"').first
            notebooks_tab.click()
            time.sleep(3)
            print("Clicked Notebooks tab")
        except:
            print("Please manually click Notebooks tab")
            input("Press Enter when on Notebooks page...")
        
        # Get all notebooks
        notebooks = get_notebooks(page)
        
        if not notebooks:
            print("No notebooks found")
            return
        
        # Download each notebook
        downloaded = 0
        for i, notebook in enumerate(notebooks):
            print(f"\nProcessing notebook {i+1}/{len(notebooks)}")
            
            if download_notebook(page, notebook):
                downloaded += 1
            
            # Go back to notebooks list
            page.goto(SNOWFLAKE_URL)
            time.sleep(3)
            try:
                notebooks_tab = page.locator('text="Notebooks"').first
                notebooks_tab.click()
                time.sleep(2)
            except:
                pass
        
        print(f"\nCompleted! Downloaded {downloaded} notebooks")
        
        # List files
        try:
            files = os.listdir(DOWNLOAD_PATH)
            print(f"Files in {DOWNLOAD_PATH}:")
            for f in files:
                print(f"  - {f}")
        except:
            pass
        
        browser.close()

if __name__ == "__main__":
    main()
