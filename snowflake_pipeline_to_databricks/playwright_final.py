from playwright.sync_api import sync_playwright
import os
import time
import glob

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"
start_url = "https://app.snowflake.com/lcdxius/rb10809/#/homepage"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def wait_for_download_completion(download_dir, timeout=30):
    """Wait for download to complete by checking for .crdownload files"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if not glob.glob(os.path.join(download_dir, "*.crdownload")):
            return True
        time.sleep(1)
    return False

def find_notebook_rows(page):
    """Find ALL notebook links in the table using Selenium approach"""
    print("🔍 Looking for ALL notebook links in the table...")

    # Wait for page to load
    page.wait_for_timeout(3000)

    # Look for ALL notebook links with href containing "/notebooks/"
    notebook_link_selectors = [
        'a[href*="/notebooks/"]',
        'a[role="link"][aria-label]',
        'a.az.bc[role="link"]'  # From Selenium script
    ]

    notebook_links = []

    for selector in notebook_link_selectors:
        try:
            links = page.locator(selector).all()

            if links:
                for link in links:
                    try:
                        if link.is_visible():
                            href = link.get_attribute('href')
                            aria_label = link.get_attribute('aria-label')

                            # Verify it's a notebook link and not a template
                            if (href and '/notebooks/' in href and
                                'template' not in href.lower() and
                                aria_label and len(aria_label) > 2):

                                notebook_links.append(link)
                                print(f"   ✅ Found notebook: {aria_label}")
                                print(f"      📍 URL: {href}")
                    except:
                        continue

            if notebook_links:
                print(f"   ✅ Found {len(notebook_links)} notebook links using: {selector}")
                break

        except Exception as e:
            continue

    # Remove duplicates
    unique_links = []
    seen_labels = set()

    for link in notebook_links:
        try:
            aria_label = link.get_attribute('aria-label')
            if aria_label and aria_label not in seen_labels:
                unique_links.append(link)
                seen_labels.add(aria_label)
        except:
            continue

    print(f"📚 Total unique notebooks found: {len(unique_links)}")
    for i, link in enumerate(unique_links, 1):
        try:
            print(f"   {i}. {link.get_attribute('aria-label')}")
        except:
            print(f"   {i}. Unknown notebook")

    return unique_links

def find_and_click_export_menu(page):
    """Find and click the 'More notebook actions' button, then export"""
    print("   🔍 Looking for 'More notebook actions' button...")

    # Wait for notebook page to fully load
    page.wait_for_timeout(3000)

    # Multiple selectors for the "More notebook actions" button (from Selenium)
    more_actions_selectors = [
        'button[aria-label="More notebook actions"]',
        'div[role="button"][aria-label="More notebook actions"]',
        'button:has-text("...")',
        'button >> svg[aria-label="Ellipsis"]',
        'button:has(svg.eq.er.bs)'  # SVG class from Selenium
    ]

    more_actions_button = None
    for selector in more_actions_selectors:
        try:
            print(f"   🔍 Trying selector: {selector}")
            elements = page.locator(selector).all()
            print(f"   📊 Found {len(elements)} elements")

            for element in elements:
                try:
                    if element.is_visible():
                        # Verify it's in the notebook header area (top portion of page)
                        box = element.bounding_box()
                        if box and box['y'] < 300:  # Header area
                            more_actions_button = element
                            print(f"   ✅ Found 'More notebook actions' button using: {selector}")
                            break
                except Exception as e:
                    continue

            if more_actions_button:
                break

        except Exception as e:
            print(f"   ⚠️  Error with selector {selector}: {e}")
            continue

    if not more_actions_button:
        print("   ❌ 'More notebook actions' button not found")
        return False

    # Click the "More notebook actions" button
    try:
        # Scroll to make sure it's visible
        more_actions_button.scroll_into_view_if_needed()
        page.wait_for_timeout(1000)

        # Try different click methods
        click_success = False
        try:
            more_actions_button.click()
            click_success = True
            print("   ✅ Direct click successful")
        except Exception as e:
            print(f"   ⚠️  Direct click failed: {e}")
            try:
                more_actions_button.click(force=True)
                click_success = True
                print("   ✅ Force click successful")
            except Exception as e:
                print(f"   ❌ All click methods failed: {e}")

        if not click_success:
            return False

        print("   📋 'More notebook actions' menu clicked")
        page.wait_for_timeout(3000)  # Wait for dropdown menu to appear

        # Take screenshot of dropdown menu for debugging
        page.screenshot(path=f"{download_dir}/dropdown_menu_{int(time.time())}.png")
        print("   📸 Dropdown menu screenshot saved")

        # Look for Export option in the dropdown menu (from Selenium)
        export_selectors = [
            'text="Export"',
            'button:has-text("Export")',
            'div:has-text("Export")',
            'li:has-text("Export")',
            'span:has-text("Export")',
            '[role="menuitem"]:has-text("Export")'
        ]

        for export_selector in export_selectors:
            try:
                print(f"   🔍 Looking for Export option with: {export_selector}")
                export_element = page.locator(export_selector).first

                if export_element.is_visible():
                    # Try different click methods for export button
                    click_success = False
                    try:
                        export_element.click()
                        click_success = True
                        print("   📤 Export option clicked (direct)")
                    except Exception as e:
                        print(f"   ⚠️  Direct click failed: {e}")
                        try:
                            export_element.click(force=True)
                            click_success = True
                            print("   📤 Export option clicked (force)")
                        except Exception as e:
                            print(f"   ❌ All export click methods failed: {e}")

                    if click_success:
                        print("   📤 Export clicked, waiting for export dialog...")
                        page.wait_for_timeout(3000)

                        # Look for the final Export button in the modal dialog (from Selenium)
                        final_export_selectors = [
                            'div[role="button"] >> text="Export"',
                            'div[role="button"]:has-text("Export"):not(:has-text("Cancel"))',
                            'button:has-text("Export"):not(:has-text("Cancel"))',
                            'div[tabindex="0"][role="button"] >> text="Export"'
                        ]

                        final_export_clicked = False
                        for selector in final_export_selectors:
                            try:
                                print(f"   🔍 Looking for final Export button with: {selector}")
                                final_export_btn = page.locator(selector).first

                                if final_export_btn.is_visible():
                                    # Ensure it's the Export button and not Cancel
                                    button_text = final_export_btn.inner_text().lower()
                                    if 'export' in button_text and 'cancel' not in button_text:
                                        print("   ✅ Found final Export button in dialog")

                                        # Try multiple click methods for the final export button
                                        try:
                                            final_export_btn.click()
                                            print("   ⬇️  Final Export button clicked (direct)")
                                            final_export_clicked = True
                                        except Exception as e:
                                            print(f"   ⚠️  Direct click failed: {e}")
                                            try:
                                                final_export_btn.click(force=True)
                                                print("   ⬇️  Final Export button clicked (force)")
                                                final_export_clicked = True
                                            except Exception as e:
                                                print(f"   ❌ All final export click methods failed: {e}")

                                        if final_export_clicked:
                                            print("   📁 Download should now be starting...")
                                            page.wait_for_timeout(2000)  # Give time for download to initiate
                                            break
                                    else:
                                        print(f"   ⚠️  Found button but text doesn't match: '{button_text}'")

                            except Exception as e:
                                print(f"   ⚠️  Error with selector {selector}: {e}")
                                continue

                        if final_export_clicked:
                            print("   ✅ Export dialog handled successfully")
                            return True
                        else:
                            print("   ❌ Failed to click final Export button in dialog")
                            return False

            except Exception as e:
                print(f"   ⚠️  Error clicking export: {e}")
                continue

        print("   ❌ Export option not found in dropdown menu")
        return False

    except Exception as e:
        print(f"   ❌ Error in export process: {e}")
        return False

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake homepage ===
    page.goto(start_url)
    print("Please log in to Snowflake...")

    input("Press Enter when you're logged in and can see the homepage...")

    # === Step 2: Navigate to Notebooks ===
    print("📚 Navigating to notebooks page...")
    page.goto(start_url)
    page.wait_for_timeout(5000)

    print(f"   Current URL: {page.url}")
    print(f"   Page title: {page.title()}")

    # === Step 3: Find notebook rows ===
    notebook_rows = find_notebook_rows(page)

    if not notebook_rows:
        print("❌ No notebook rows found.")
        print("   Please check if you're on the correct page and notebooks are visible.")
        input("   Press Enter to exit...")
        return

    print(f"📚 Found {len(notebook_rows)} notebook rows to process")
    
    # === Step 4: Process each notebook ===
    downloaded_count = 0
    failed_count = 0

    for i in range(len(notebook_rows)):
        try:
            print(f"\n📖 Processing notebook #{i+1}/{len(notebook_rows)}")

            # Re-find notebook rows to avoid stale element reference
            current_notebook_rows = find_notebook_rows(page)
            if not current_notebook_rows or i >= len(current_notebook_rows):
                print("   ⚠️  Could not re-find notebook rows, stopping...")
                break

            notebook_row = current_notebook_rows[i]

            # Get notebook info from the row
            try:
                aria_label = notebook_row.get_attribute('aria-label')
                print(f"   Notebook: {aria_label}")
            except:
                print(f"   Notebook: Row #{i+1}")

            # Scroll to the row
            notebook_row.scroll_into_view_if_needed()
            page.wait_for_timeout(2000)

            # Click the notebook row
            try:
                notebook_row.click()
            except:
                try:
                    notebook_row.click(force=True)
                except Exception as e:
                    print(f"   ❌ Failed to click notebook: {e}")
                    continue

            print("   ✅ Notebook row clicked")
            page.wait_for_timeout(5000)  # Wait for notebook to load

            # Check if we're actually in the notebook view
            current_url = page.url
            print(f"   📍 Current URL after click: {current_url}")

            if "notebooks" not in current_url or "/notebooks/" in current_url:
                print("   ✅ Successfully opened notebook")
            else:
                print("   ⚠️  May not have opened notebook properly")

            # Look for the export functionality
            download_found = False

            print("   🔍 Looking for export functionality...")

            # Use the export function based on Selenium approach
            if find_and_click_export_menu(page):
                # Count files before and after export
                files_before = len(os.listdir(download_dir))

                # Wait for export/download to complete
                if wait_for_download_completion(download_dir, 15):
                    files_after = len(os.listdir(download_dir))
                    if files_after > files_before:
                        print("   ✅ Export/Download completed successfully")
                        downloaded_count += 1
                        download_found = True
                    else:
                        print("   ⚠️  No new files detected after export")
                        failed_count += 1
                else:
                    print("   ⚠️  Export/Download timeout")
                    failed_count += 1
            else:
                print("   ❌ Export functionality not accessible")
                failed_count += 1

            if not download_found:
                failed_count += 1

            # Wait before going back
            print("   ⏳ Waiting before returning to notebooks list...")
            page.wait_for_timeout(2000)

            # Return to notebooks list
            print("   🔙 Returning to notebooks list...")
            page.goto(start_url)
            page.wait_for_timeout(4000)

        except Exception as e:
            print(f"   ❌ Error processing notebook #{i+1}: {str(e)}")
            failed_count += 1
            try:
                page.goto(start_url)
                page.wait_for_timeout(3000)
            except:
                pass

    # === Summary ===
    print(f"\n📊 Download Summary:")
    print(f"   ✅ Successfully downloaded: {downloaded_count}")
    print(f"   ❌ Failed downloads: {failed_count}")
    print(f"   📁 Download directory: {download_dir}")

    # List downloaded files
    try:
        downloaded_files = os.listdir(download_dir)
        if downloaded_files:
            print(f"\n📄 Downloaded files:")
            for file in downloaded_files:
                file_path = os.path.join(download_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   - {file} ({file_size} bytes)")
        else:
            print("\n📄 No files were downloaded")
    except Exception as e:
        print(f"\n❌ Error listing files: {e}")

    print("\n✅ Script completed!")
    browser.close()




if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
