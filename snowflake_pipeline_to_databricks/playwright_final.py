from playwright.sync_api import sync_playwright
import os
import time

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake homepage ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/homepage"
    page.goto(start_url)
    print("Please log in to Snowflake...")
    
    input("Press Enter when you're logged in and can see the homepage...")
    
    # === Step 2: Navigate to Notebooks ===
    print("Navigating to Notebooks...")
    try:
        # Wait for page to load
        page.wait_for_timeout(3000)
        
        # Click on the Notebooks tab using the exact selector from your HTML
        notebooks_tab = page.locator('[role="tab"]:has-text("Notebooks")')
        if notebooks_tab.count() > 0:
            notebooks_tab.click()
            page.wait_for_timeout(3000)
            print("✅ Clicked on Notebooks tab")
        else:
            # Fallback to text selector
            notebooks_tab = page.locator('text="Notebooks"').first
            notebooks_tab.click()
            page.wait_for_timeout(3000)
            print("✅ Clicked on Notebooks tab (fallback)")
    except Exception as e:
        print(f"Could not click Notebooks tab: {e}")
        input("Please manually click on the 'Notebooks' tab and press Enter...")
    
    # === Step 3: Process all notebooks ===
    print("Processing notebooks...")
    
    # List of notebook names from your screenshot
    notebook_names = [
        "MPRASANNAPAUL 2025-07-25 16:54:13",
        "Translate multilingual customer reviews",
        "Reference cells and variables in Notebooks",
        "MPRASANNAPAUL 2025-07-25 16:59:15"
    ]
    
    for i, notebook_name in enumerate(notebook_names, 1):
        print(f"\n=== Processing {i}/{len(notebook_names)}: {notebook_name} ===")
        
        try:
            # Find and click the notebook
            success = click_notebook_by_name(page, notebook_name)
            if not success:
                print(f"❌ Could not click on notebook: {notebook_name}")
                continue
            
            # Wait for notebook to load
            page.wait_for_timeout(5000)
            
            # Download the notebook
            download_success = download_current_notebook(page, notebook_name, download_dir)
            
            if download_success:
                print(f"✅ Successfully downloaded: {notebook_name}")
            else:
                print(f"❌ Failed to download: {notebook_name}")
            
            # Return to notebooks list
            print("Returning to notebooks list...")
            page.goto(start_url)
            page.wait_for_timeout(3000)
            
            # Click Notebooks tab again
            try:
                notebooks_tab = page.locator('[role="tab"]:has-text("Notebooks")')
                if notebooks_tab.count() > 0:
                    notebooks_tab.click()
                else:
                    page.locator('text="Notebooks"').first.click()
                page.wait_for_timeout(3000)
            except:
                if i < len(notebook_names):  # Not the last notebook
                    input("Please navigate back to the Notebooks tab and press Enter...")
            
        except Exception as e:
            print(f"Error processing {notebook_name}: {e}")
            continue
    
    print("\n✅ All notebooks processed!")
    
    # List downloaded files
    print(f"\n=== DOWNLOADED FILES ===")
    try:
        files = [f for f in os.listdir(download_dir) if f.endswith('.sql')]
        if files:
            for file in files:
                print(f"✅ {file}")
        else:
            print("No .sql files found")
    except:
        print("Could not list files")
    
    browser.close()

def click_notebook_by_name(page, notebook_name):
    """Find and click a notebook by its name"""
    print(f"Looking for notebook: {notebook_name}")
    
    # Try different approaches to find the notebook
    selectors = [
        f'tr:has-text("{notebook_name}")',
        f'td:has-text("{notebook_name}")',
        f'*:has-text("{notebook_name}")',
        # Try partial matches for long names
        f'tr:has-text("{notebook_name[:20]}")',  # First 20 characters
        f'td:has-text("{notebook_name[:20]}")'
    ]
    
    for selector in selectors:
        try:
            elements = page.locator(selector)
            count = elements.count()
            print(f"Selector '{selector}': found {count} elements")
            
            if count > 0:
                # Try to click the first matching element
                element = elements.first
                
                # If it's a table row, click the first cell
                if 'tr:' in selector:
                    try:
                        first_cell = element.locator('td').first
                        first_cell.click()
                        print(f"✅ Clicked notebook via row selector")
                        return True
                    except:
                        # Fallback to clicking the row itself
                        element.click()
                        print(f"✅ Clicked notebook via row click")
                        return True
                else:
                    # Click the element directly
                    element.click()
                    print(f"✅ Clicked notebook via direct click")
                    return True
                    
        except Exception as e:
            print(f"Selector '{selector}' failed: {e}")
            continue
    
    print(f"❌ Could not find clickable element for: {notebook_name}")
    return False

def download_current_notebook(page, notebook_name, download_dir):
    """Download the currently open notebook"""
    print("Waiting for notebook to fully load...")

    # Wait longer for notebook to load
    page.wait_for_timeout(8000)

    # Check if we're actually in a notebook by looking for notebook-specific elements
    print("Verifying we're in a notebook...")
    notebook_indicators = [
        'text="Files"',  # Files tab in notebook
        'text="Databases"',  # Databases tab in notebook
        'text="Python"',  # Python selector
        'text="Run all"',  # Run all button
        '.notebook',  # Notebook container
        '[data-testid*="notebook"]'
    ]

    in_notebook = False
    for indicator in notebook_indicators:
        try:
            if page.locator(indicator).count() > 0:
                print(f"✅ Confirmed in notebook (found: {indicator})")
                in_notebook = True
                break
        except:
            continue

    if not in_notebook:
        print("❌ Not in a notebook interface")
        return False

    # Take screenshot
    page.screenshot(path=os.path.join(download_dir, f"notebook_{notebook_name.replace(' ', '_')[:30]}.png"))

    # Debug: List ALL buttons on the page
    print("=== DEBUGGING ALL BUTTONS ===")
    try:
        all_buttons = page.locator('button:visible')
        button_count = all_buttons.count()
        print(f"Found {button_count} visible buttons")

        for i in range(min(15, button_count)):
            try:
                button = all_buttons.nth(i)
                text = button.inner_text().strip()
                aria_label = button.get_attribute('aria-label') or ''
                title = button.get_attribute('title') or ''
                class_name = button.get_attribute('class') or ''

                print(f"Button {i}: text='{text}' aria='{aria_label}' title='{title}' class='{class_name[:50]}'")

                # Check if this looks like a menu button
                if ('...' in text or
                    'more' in aria_label.lower() or
                    'menu' in aria_label.lower() or
                    'more' in title.lower() or
                    'action' in class_name.lower()):
                    print(f"*** POTENTIAL MENU BUTTON: {i} ***")
            except:
                pass
    except Exception as debug_error:
        print(f"Button debug failed: {debug_error}")

    print("=== END BUTTON DEBUG ===")

    # Look for the three dots menu button
    print("Looking for menu button...")

    menu_selectors = [
        'button:has-text("...")',
        'button[aria-label*="More"]',
        'button[title*="More"]',
        'button[aria-haspopup="menu"]',
        'button[aria-label*="menu"]',
        # Try to find it in specific areas
        'header button',
        '.toolbar button',
        '.notebook-header button'
    ]

    menu_clicked = False

    # First try specific selectors
    for selector in menu_selectors:
        try:
            buttons = page.locator(selector)
            if buttons.count() > 0:
                print(f"Found menu button with: {selector}")
                buttons.first.click()
                page.wait_for_timeout(2000)
                menu_clicked = True
                break
        except Exception as e:
            print(f"Selector {selector} failed: {e}")
            continue

    # If not found, try manual detection
    if not menu_clicked:
        print("Trying manual button detection...")
        try:
            all_buttons = page.locator('button:visible')
            for i in range(min(20, all_buttons.count())):
                try:
                    button = all_buttons.nth(i)
                    text = button.inner_text().strip()
                    aria_label = button.get_attribute('aria-label') or ''
                    title = button.get_attribute('title') or ''

                    if ('...' in text or
                        'more' in aria_label.lower() or
                        'menu' in aria_label.lower() or
                        'more' in title.lower()):
                        print(f"Found potential menu button {i}: {text} / {aria_label} / {title}")
                        button.click()
                        page.wait_for_timeout(2000)
                        menu_clicked = True
                        break
                except:
                    continue
        except:
            pass
    
    if not menu_clicked:
        print("❌ Could not find menu button automatically")
        print("Please manually click the three dots (...) menu button")
        input("Press Enter AFTER you've clicked the menu button...")
        menu_clicked = True  # Assume user clicked it
    
    # Take screenshot of menu
    page.screenshot(path=os.path.join(download_dir, f"menu_{notebook_name.replace(' ', '_')[:30]}.png"))
    
    # Look for Download option
    print("Looking for Download option...")
    
    download_selectors = [
        'text="Download"',
        '*:has-text("Download")',
        'div:has-text("Download")',
        'span:has-text("Download")',
        'button:has-text("Download")'
    ]
    
    for selector in download_selectors:
        try:
            download_elements = page.locator(selector)
            if download_elements.count() > 0:
                print(f"Found download option with: {selector}")
                
                # Start download
                with page.expect_download(timeout=15000) as download_info:
                    download_elements.first.click()
                
                # Save download
                download = download_info.value
                safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                download.save_as(download_path)
                print(f"✅ Downloaded: {download_path}")
                return True
                
        except Exception as e:
            print(f"Download attempt failed with {selector}: {e}")
            continue
    
    print("❌ Could not find or click Download option")
    return False

if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
