from playwright.sync_api import sync_playwright
import os
import time

# === Configuration ===
download_dir = "/home/<USER>/playwright_script"

# === Ensure directory exists ===
if not os.path.exists(download_dir):
    os.makedirs(download_dir)

def run(playwright):
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context(accept_downloads=True)
    page = context.new_page()

    # === Step 1: Open Snowflake homepage ===
    start_url = "https://app.snowflake.com/lcdxius/rb10809/#/homepage"
    page.goto(start_url)
    print("Please log in to Snowflake...")
    
    input("Press Enter when you're logged in and can see the homepage...")
    
    # === Step 2: Navigate to Notebooks ===
    print("Navigating to Notebooks...")
    try:
        # Click on the Notebooks tab
        notebooks_tab = page.locator('text="Notebooks"').first
        notebooks_tab.click()
        page.wait_for_timeout(3000)
        print("✅ Clicked on Notebooks tab")
    except Exception as e:
        print(f"Could not click Notebooks tab automatically: {e}")
        input("Please manually click on the 'Notebooks' tab and press Enter...")
    
    # Take screenshot of notebooks page
    page.screenshot(path=os.path.join(download_dir, "notebooks_list.png"))
    print("Screenshot of notebooks list saved")
    
    # === Step 3: Find and process notebooks ===
    print("Looking for notebooks in the table...")
    
    # Look for table rows that contain "Notebook" in the TYPE column
    notebook_rows = page.locator('tr:has-text("Notebook")')
    count = notebook_rows.count()
    print(f"Found {count} notebooks")
    
    if count == 0:
        print("No notebooks found automatically. Let's try manual approach...")
        manual_download_all_notebooks(page, download_dir)
        return
    
    # Process each notebook
    for i in range(count):
        try:
            print(f"\n=== Processing notebook {i+1}/{count} ===")
            
            # Get the notebook row
            notebook_row = notebook_rows.nth(i)
            
            # Get notebook name from the first cell (TITLE column)
            try:
                notebook_name = notebook_row.locator('td').first.inner_text().strip()
                print(f"Notebook name: {notebook_name}")
            except:
                notebook_name = f"notebook_{i+1}"
                print(f"Could not get name, using: {notebook_name}")
            
            # Click on the notebook name to open it
            try:
                # Try clicking on the first cell (notebook title)
                title_cell = notebook_row.locator('td').first
                title_cell.click()
                print("✅ Clicked on notebook")
            except Exception as click_error:
                print(f"Failed to click notebook: {click_error}")
                continue
            
            # Wait for notebook to load
            page.wait_for_timeout(5000)
            
            # Download the notebook
            success = download_current_notebook(page, notebook_name, download_dir)
            
            if success:
                print(f"✅ Successfully processed: {notebook_name}")
            else:
                print(f"❌ Failed to download: {notebook_name}")
            
            # Go back to notebooks list
            print("Returning to notebooks list...")
            page.goto(start_url)
            page.wait_for_timeout(2000)
            
            # Click Notebooks tab again
            try:
                notebooks_tab = page.locator('text="Notebooks"').first
                notebooks_tab.click()
                page.wait_for_timeout(3000)
            except:
                input("Please navigate back to the Notebooks tab and press Enter...")
            
        except Exception as notebook_error:
            print(f"Error processing notebook {i+1}: {notebook_error}")
            continue
    
    print("\n✅ All notebooks processed!")
    browser.close()

def download_current_notebook(page, notebook_name, download_dir):
    """Download the currently open notebook"""
    print("Looking for the three dots menu...")
    
    # Take screenshot of the notebook
    page.screenshot(path=os.path.join(download_dir, f"notebook_{notebook_name.replace(' ', '_')}.png"))
    
    # Look for the three dots menu button
    menu_button_found = False
    menu_selectors = [
        'button:has-text("...")',
        'button[aria-label*="More"]',
        'button[title*="More"]',
        'button[aria-haspopup="menu"]'
    ]
    
    for selector in menu_selectors:
        try:
            menu_buttons = page.locator(selector)
            if menu_buttons.count() > 0:
                print(f"Found menu button with selector: {selector}")
                menu_buttons.first.click()
                page.wait_for_timeout(2000)
                menu_button_found = True
                break
        except:
            continue
    
    if not menu_button_found:
        print("Could not find menu button automatically")
        print("Please manually click the three dots (...) menu button")
        input("Press Enter after clicking the menu button...")
    
    # Take screenshot of the menu
    page.screenshot(path=os.path.join(download_dir, f"menu_{notebook_name.replace(' ', '_')}.png"))
    
    # Look for Download option
    print("Looking for Download option...")
    
    download_success = False
    download_selectors = [
        'text="Download"',  # Exact text match
        '*:has-text("Download")',
        'div:has-text("Download")',
        'span:has-text("Download")'
    ]
    
    for selector in download_selectors:
        try:
            download_elements = page.locator(selector)
            if download_elements.count() > 0:
                print(f"Found download option with selector: {selector}")
                
                # Try to click and download
                with page.expect_download(timeout=15000) as download_info:
                    download_elements.first.click()
                
                # Save the download
                download = download_info.value
                safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
                download_path = os.path.join(download_dir, f"{safe_filename}.sql")
                download.save_as(download_path)
                print(f"✅ Downloaded: {download_path}")
                download_success = True
                break
                
        except Exception as download_error:
            print(f"Download attempt failed with {selector}: {download_error}")
            continue
    
    if not download_success:
        print("Automatic download failed. Trying manual approach...")
        print("Please manually click on 'Download' in the menu")
        
        try:
            with page.expect_download(timeout=30000) as download_info:
                input("Press Enter AFTER you've clicked Download...")
            
            download = download_info.value
            safe_filename = "".join(c for c in notebook_name if c.isalnum() or c in (' ', '_', '-')).rstrip()
            download_path = os.path.join(download_dir, f"{safe_filename}.sql")
            download.save_as(download_path)
            print(f"✅ Manual download saved: {download_path}")
            download_success = True
            
        except Exception as manual_error:
            print(f"Manual download also failed: {manual_error}")
    
    return download_success

def manual_download_all_notebooks(page, download_dir):
    """Fallback manual approach"""
    print("\n=== MANUAL DOWNLOAD MODE ===")
    
    notebook_names = [
        "MPRASANNAPAUL 2025-07-25 16:54:13",
        "Translate multilingual customer reviews", 
        "Reference cells and variables in Notebooks"
    ]
    
    for notebook_name in notebook_names:
        print(f"\n--- Processing: {notebook_name} ---")
        print("1. Click on the notebook in the table to open it")
        print("2. Wait for it to load completely")
        input("Press Enter when the notebook is open...")
        
        success = download_current_notebook(page, notebook_name, download_dir)
        
        if success:
            print(f"✅ {notebook_name} downloaded successfully")
        else:
            print(f"❌ Failed to download {notebook_name}")
        
        print("Please navigate back to the Notebooks list")
        input("Press Enter when you're back at the notebooks list...")

if __name__ == "__main__":
    with sync_playwright() as playwright:
        run(playwright)
    
    # List downloaded files
    print(f"\n=== DOWNLOADED FILES ===")
    try:
        files = [f for f in os.listdir(download_dir) if f.endswith('.sql')]
        if files:
            for file in files:
                print(f"✅ {file}")
        else:
            print("No .sql files found")
    except:
        print("Could not list files")
