import os
import base64
import requests

# Configuration
DATABRICKS_INSTANCE = "https://dbc-be36d720-1128.cloud.databricks.com/"
TOKEN = "************************************"
LOCAL_FOLDER = "/home/<USER>/playwright_script"  # Updated to match your download path
WORKSPACE_BASE_PATH = "/Workspace/snowflake_notebooks"  # More descriptive name

def upload_to_databricks(file_path, workspace_path):
    """Upload a SQL file to Databricks workspace"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            source_code = f.read()

        encoded_content = base64.b64encode(source_code.encode("utf-8")).decode("utf-8")

        url = f"{DATABRICKS_INSTANCE}/api/2.0/workspace/import"
        headers = {
            "Authorization": f"Bearer {TOKEN}"
        }
        data = {
            "path": workspace_path,
            "format": "SOURCE",
            "language": "SQL",  # Changed from PYTHON to SQL since notebooks are .sql files
            "content": encoded_content,
            "overwrite": True
        }

        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            print(f"Uploaded: {workspace_path}")
            return True
        else:
            print(f"Failed to upload {file_path}: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error uploading {file_path}: {e}")
        return False

def create_databricks_folder(folder_path):
    """Create folder in Databricks workspace if it doesn't exist"""
    url = f"{DATABRICKS_INSTANCE}/api/2.0/workspace/mkdirs"
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    data = {
        "path": folder_path
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        print(f"Created/verified folder: {folder_path}")
        return True
    else:
        print(f"Failed to create folder {folder_path}: {response.text}")
        return False

def main():
    """Main function to upload all downloaded notebooks"""
    if not os.path.exists(LOCAL_FOLDER):
        print(f"Local folder not found: {LOCAL_FOLDER}")
        return

    # Create Databricks folder first
    print(f"Creating Databricks folder: {WORKSPACE_BASE_PATH}")
    if not create_databricks_folder(WORKSPACE_BASE_PATH):
        print("Failed to create Databricks folder, continuing anyway...")

    # Get all SQL files (downloaded notebooks)
    sql_files = [f for f in os.listdir(LOCAL_FOLDER) if f.endswith('.sql')]

    if not sql_files:
        print("No SQL files found to upload")
        return

    print(f"Found {len(sql_files)} SQL files to upload")
    
    uploaded = 0
    failed = 0
    
    for filename in sql_files:
        full_path = os.path.join(LOCAL_FOLDER, filename)
        
        # Clean filename for workspace (remove .sql extension and clean up)
        clean_name = filename[:-4]  # Remove .sql
        workspace_path = f"{WORKSPACE_BASE_PATH}/{clean_name}"
        
        print(f"Uploading: {filename}")
        
        if upload_to_databricks(full_path, workspace_path):
            uploaded += 1
        else:
            failed += 1

    print(f"\nUpload Summary:")
    print(f"   Successfully uploaded: {uploaded}")
    print(f"   Failed uploads: {failed}")
    print(f"   Databricks location: {WORKSPACE_BASE_PATH}")

if __name__ == "__main__":
    main()