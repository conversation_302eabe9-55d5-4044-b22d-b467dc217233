import os
import base64
import requests
import json

# Configuration
DATABRICKS_INSTANCE = "https://dbc-be36d720-1128.cloud.databricks.com/"
TOKEN = "************************************"
LOCAL_FOLDER = "/home/<USER>/playwright_script"  # Updated to match your download path
WORKSPACE_BASE_PATH = "/Workspace/snowflake_notebooks"  # More descriptive name

def parse_snowflake_notebook(file_path):
    """Parse Snowflake notebook JSON and extract code cells"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Try to parse as JSON (Snowflake notebook format)
        try:
            notebook_data = json.loads(content)
            cells = notebook_data.get("cells", [])

            sql_code = []
            python_code = []

            for cell in cells:
                cell_type = cell.get("metadata", {}).get("language", "")
                source = cell.get("source", "")

                if source.strip():  # Only include non-empty cells
                    if cell_type == "sql":
                        sql_code.append(f"-- Cell: {cell.get('id', 'unknown')}")
                        sql_code.append(source)
                        sql_code.append("")  # Empty line between cells
                    elif cell_type == "python":
                        python_code.append(f"# Cell: {cell.get('id', 'unknown')}")
                        python_code.append(source)
                        python_code.append("")  # Empty line between cells

            return {
                "sql": "\n".join(sql_code) if sql_code else None,
                "python": "\n".join(python_code) if python_code else None,
                "is_notebook": True
            }

        except json.JSONDecodeError:
            # If it's not JSON, treat as plain SQL
            return {
                "sql": content,
                "python": None,
                "is_notebook": False
            }

    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return None

def upload_to_databricks(workspace_path, language, source_code):
    """Upload code to Databricks workspace"""
    try:
        encoded_content = base64.b64encode(source_code.encode("utf-8")).decode("utf-8")

        url = f"{DATABRICKS_INSTANCE}/api/2.0/workspace/import"
        headers = {
            "Authorization": f"Bearer {TOKEN}"
        }
        data = {
            "path": workspace_path,
            "format": "SOURCE",
            "language": language,
            "content": encoded_content,
            "overwrite": True
        }

        response = requests.post(url, headers=headers, json=data)

        if response.status_code == 200:
            print(f"Uploaded: {workspace_path}")
            return True
        else:
            print(f"Failed to upload to {workspace_path}: {response.text}")
            return False

    except Exception as e:
        print(f"Error uploading to {workspace_path}: {e}")
        return False

def create_databricks_folder(folder_path):
    """Create folder in Databricks workspace if it doesn't exist"""
    url = f"{DATABRICKS_INSTANCE}/api/2.0/workspace/mkdirs"
    headers = {
        "Authorization": f"Bearer {TOKEN}"
    }
    data = {
        "path": folder_path
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        print(f"Created/verified folder: {folder_path}")
        return True
    else:
        print(f"Failed to create folder {folder_path}: {response.text}")
        return False

def main():
    """Main function to upload all downloaded notebooks"""
    if not os.path.exists(LOCAL_FOLDER):
        print(f"Local folder not found: {LOCAL_FOLDER}")
        return

    # Create Databricks folder first
    print(f"Creating Databricks folder: {WORKSPACE_BASE_PATH}")
    if not create_databricks_folder(WORKSPACE_BASE_PATH):
        print("Failed to create Databricks folder, continuing anyway...")

    # Get all SQL files (downloaded notebooks)
    sql_files = [f for f in os.listdir(LOCAL_FOLDER) if f.endswith('.sql')]

    if not sql_files:
        print("No SQL files found to upload")
        return

    print(f"Found {len(sql_files)} SQL files to upload")
    
    uploaded = 0
    failed = 0

    for filename in sql_files:
        full_path = os.path.join(LOCAL_FOLDER, filename)

        print(f"Processing: {filename}")

        # Parse the Snowflake notebook
        parsed_content = parse_snowflake_notebook(full_path)

        if not parsed_content:
            print(f"Failed to parse {filename}")
            failed += 1
            continue

        # Clean filename for workspace (remove .sql extension)
        clean_name = filename[:-4]  # Remove .sql

        # Upload SQL content if exists
        if parsed_content["sql"]:
            sql_workspace_path = f"{WORKSPACE_BASE_PATH}/{clean_name}_SQL"
            print(f"  Uploading SQL content to: {sql_workspace_path}")

            if upload_to_databricks(sql_workspace_path, "SQL", parsed_content["sql"]):
                uploaded += 1
                print(f"  SQL content uploaded successfully")
            else:
                failed += 1

        # Upload Python content if exists
        if parsed_content["python"]:
            python_workspace_path = f"{WORKSPACE_BASE_PATH}/{clean_name}_Python"
            print(f"  Uploading Python content to: {python_workspace_path}")

            if upload_to_databricks(python_workspace_path, "PYTHON", parsed_content["python"]):
                uploaded += 1
                print(f"  Python content uploaded successfully")
            else:
                failed += 1

        # If neither SQL nor Python content found
        if not parsed_content["sql"] and not parsed_content["python"]:
            print(f"  No executable content found in {filename}")
            failed += 1

    print(f"\nUpload Summary:")
    print(f"   Successfully uploaded: {uploaded}")
    print(f"   Failed uploads: {failed}")
    print(f"   Databricks location: {WORKSPACE_BASE_PATH}")

if __name__ == "__main__":
    main()